<?php
/**
 * Create Notification System Tables and Functions
 * 
 * This script creates the necessary database tables and functions for the notification system
 */

require_once 'config.php';

echo "Creating notification system...\n\n";

try {
    // Create main notifications table
    echo "Creating notifications table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS notifications (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            recipient_id INT(11) NOT NULL,
            sender_id INT(11) NULL,
            sender_type ENUM('admin', 'member') NOT NULL DEFAULT 'admin',
            notification_type ENUM('announcement', 'message', 'birthday', 'event', 'donation', 'system') NOT NULL DEFAULT 'message',
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            action_url VARCHAR(500) NULL,
            is_read TINYINT(1) NOT NULL DEFAULT 0,
            priority ENUM('low', 'normal', 'high', 'urgent') NOT NULL DEFAULT 'normal',
            expires_at DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP NULL,
            INDEX idx_recipient_read (recipient_id, is_read),
            INDEX idx_created_at (created_at),
            INDEX idx_sender (sender_id, sender_type),
            INDEX idx_type (notification_type),
            FOREIGN KEY (recipient_id) REFERENCES members(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    echo "✓ Notifications table created\n";

    // Create notification preferences table
    echo "Creating notification_preferences table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS notification_preferences (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            user_id INT(11) NOT NULL,
            notification_type ENUM('announcement', 'message', 'birthday', 'event', 'donation', 'system') NOT NULL,
            email_enabled TINYINT(1) NOT NULL DEFAULT 1,
            web_enabled TINYINT(1) NOT NULL DEFAULT 1,
            sms_enabled TINYINT(1) NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_type (user_id, notification_type),
            FOREIGN KEY (user_id) REFERENCES members(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    echo "✓ Notification preferences table created\n";

    // Create notification_read_status table for tracking read status
    echo "Creating notification_read_status table...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS notification_read_status (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            notification_id INT(11) NOT NULL,
            user_id INT(11) NOT NULL,
            read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_notification_user (notification_id, user_id),
            FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES members(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    echo "✓ Notification read status table created\n";

    // Insert default notification preferences for existing users
    echo "Setting up default notification preferences...\n";
    $pdo->exec("
        INSERT IGNORE INTO notification_preferences (user_id, notification_type, email_enabled, web_enabled, sms_enabled)
        SELECT m.id, 'announcement', 1, 1, 0 FROM members m
        UNION ALL
        SELECT m.id, 'message', 1, 1, 0 FROM members m
        UNION ALL
        SELECT m.id, 'birthday', 1, 1, 0 FROM members m
        UNION ALL
        SELECT m.id, 'event', 1, 1, 0 FROM members m
        UNION ALL
        SELECT m.id, 'donation', 1, 1, 0 FROM members m
        UNION ALL
        SELECT m.id, 'system', 1, 1, 0 FROM members m
    ");
    echo "✓ Default notification preferences set\n";

    // Create some sample notifications for testing
    echo "Creating sample notifications...\n";
    
    // Get first few users for testing
    $stmt = $pdo->query("SELECT id FROM members LIMIT 3");
    $users = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($users)) {
        foreach ($users as $userId) {
            // Admin announcement
            $pdo->prepare("
                INSERT INTO notifications (recipient_id, sender_type, notification_type, title, message, priority)
                VALUES (?, 'admin', 'announcement', 'Welcome to the Notification System!', 'We have implemented a new notification system to keep you updated with important announcements and messages.', 'normal')
            ")->execute([$userId]);
            
            // System notification
            $pdo->prepare("
                INSERT INTO notifications (recipient_id, sender_type, notification_type, title, message, priority)
                VALUES (?, 'admin', 'system', 'System Update', 'The church management system has been updated with new features including this notification system.', 'low')
            ")->execute([$userId]);
        }
        echo "✓ Sample notifications created\n";
    }

    echo "\n=== Notification System Setup Complete ===\n";
    echo "Tables created:\n";
    echo "- notifications (main notification storage)\n";
    echo "- notification_preferences (user preferences)\n";
    echo "- notification_read_status (read tracking)\n";
    echo "\nFeatures available:\n";
    echo "- Admin-to-user notifications\n";
    echo "- Member-to-member notifications\n";
    echo "- Multiple notification types\n";
    echo "- Read/unread status tracking\n";
    echo "- User preferences management\n";
    echo "- Priority levels\n";
    echo "- Expiration dates\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
