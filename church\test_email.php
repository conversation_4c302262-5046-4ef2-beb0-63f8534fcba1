<?php
/**
 * Email System Test Script
 * Use this to test and debug email functionality
 */

require_once 'config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Simple authentication check (admin only)
if (!isset($_SESSION['admin_id'])) {
    die("Access denied. Admin login required.");
}

$test_result = null;
$error_message = null;

if ($_POST['action'] ?? '' === 'test_email') {
    $test_email = $_POST['test_email'] ?? '';
    
    if (!filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        $error_message = "Please enter a valid email address.";
    } else {
        // Test email sending
        $subject = "Email System Test - " . date('Y-m-d H:i:s');
        $body = "
        <h2>Email System Test</h2>
        <p>This is a test email from your church management system.</p>
        <p><strong>Test Details:</strong></p>
        <ul>
            <li>Sent at: " . date('Y-m-d H:i:s') . "</li>
            <li>Server: " . $_SERVER['SERVER_NAME'] . "</li>
            <li>PHP Version: " . PHP_VERSION . "</li>
        </ul>
        <p>If you received this email, your email system is working correctly!</p>
        ";
        
        $result = sendEmailWithPHPMailer(
            $test_email,
            $subject,
            $body,
            get_organization_name(),
            'noreply@' . $_SERVER['SERVER_NAME'],
            true
        );
        
        $test_result = $result;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email System Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="bi bi-envelope-check"></i> Email System Test</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($test_result): ?>
                            <?php if ($test_result['success']): ?>
                                <div class="alert alert-success">
                                    <h5>✅ Email Sent Successfully!</h5>
                                    <p><?php echo htmlspecialchars($test_result['message']); ?></p>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <h5>❌ Email Failed</h5>
                                    <p><?php echo htmlspecialchars($test_result['message']); ?></p>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <?php if ($error_message): ?>
                            <div class="alert alert-warning">
                                <?php echo htmlspecialchars($error_message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <input type="hidden" name="action" value="test_email">
                            <div class="mb-3">
                                <label for="test_email" class="form-label">Test Email Address</label>
                                <input type="email" class="form-control" id="test_email" name="test_email" 
                                       value="<?php echo htmlspecialchars($_POST['test_email'] ?? ''); ?>" 
                                       placeholder="Enter email address to test" required>
                                <div class="form-text">Enter an email address where you can receive the test email.</div>
                            </div>
                            <button type="submit" class="btn btn-primary">Send Test Email</button>
                        </form>
                        
                        <hr>
                        
                        <h5>Email Configuration Status</h5>
                        <?php
                        // Check email configuration
                        try {
                            $stmt = $pdo->query("SELECT setting_key, setting_value FROM email_settings");
                            $email_settings = [];
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                $email_settings[$row['setting_key']] = $row['setting_value'];
                            }
                            
                            $required_settings = ['smtp_host', 'smtp_username', 'smtp_password', 'smtp_port'];
                            $missing_settings = [];
                            
                            foreach ($required_settings as $setting) {
                                if (empty($email_settings[$setting])) {
                                    $missing_settings[] = $setting;
                                }
                            }
                            
                            if (empty($missing_settings)) {
                                echo '<div class="alert alert-success">✅ All required email settings are configured.</div>';
                            } else {
                                echo '<div class="alert alert-warning">⚠️ Missing email settings: ' . implode(', ', $missing_settings) . '</div>';
                            }
                            
                            // Show current settings (masked)
                            echo '<table class="table table-sm">';
                            foreach ($email_settings as $key => $value) {
                                $display_value = $key === 'smtp_password' ? str_repeat('*', strlen($value)) : $value;
                                echo '<tr><td>' . htmlspecialchars($key) . '</td><td>' . htmlspecialchars($display_value) . '</td></tr>';
                            }
                            echo '</table>';
                            
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger">Error checking email configuration: ' . htmlspecialchars($e->getMessage()) . '</div>';
                        }
                        ?>
                        
                        <div class="mt-3">
                            <a href="admin/email_settings.php" class="btn btn-outline-primary">Configure Email Settings</a>
                            <a href="admin/dashboard.php" class="btn btn-outline-secondary">Back to Admin</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
