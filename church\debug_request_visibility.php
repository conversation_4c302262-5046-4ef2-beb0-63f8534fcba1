<?php
/**
 * Debug Request Visibility Issues
 * 
 * This script investigates why requests are not visible to other users
 */

require_once 'config.php';

echo "=== REQUEST VISIBILITY DEBUG ===\n\n";

try {
    // 1. Check prayer_requests table structure
    echo "1. PRAYER_REQUESTS TABLE STRUCTURE:\n";
    $stmt = $pdo->query("DESCRIBE prayer_requests");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']}) - {$column['Null']} - {$column['Default']}\n";
    }
    
    // 2. Check all requests and their privacy levels
    echo "\n2. ALL REQUESTS WITH PRIVACY LEVELS:\n";
    $stmt = $pdo->query("
        SELECT pr.id, pr.member_id, pr.title, pr.privacy_level, pr.status, pr.created_at,
               m.full_name, m.first_name, m.last_name
        FROM prayer_requests pr
        JOIN members m ON pr.member_id = m.id
        ORDER BY pr.created_at DESC
        LIMIT 10
    ");
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($requests as $request) {
        echo "ID: {$request['id']} | User: {$request['full_name']} | Privacy: {$request['privacy_level']} | Status: {$request['status']} | Title: " . substr($request['title'], 0, 50) . "\n";
    }
    
    // 3. Test the community requests query
    echo "\n3. TESTING COMMUNITY REQUESTS QUERY:\n";
    echo "Query used in requests.php:\n";
    $query = "
        SELECT pr.*, m.full_name, m.first_name,
               COUNT(DISTINCT prr.id) as response_count,
               MAX(CASE WHEN prr.member_id = ? THEN 1 ELSE 0 END) as i_responded
        FROM prayer_requests pr
        JOIN members m ON pr.member_id = m.id
        LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
        WHERE pr.member_id != ? 
        AND pr.privacy_level IN ('members', 'public')
        AND pr.status = 'active'
        GROUP BY pr.id
        ORDER BY pr.is_urgent DESC, pr.created_at DESC
        LIMIT 20
    ";
    echo $query . "\n\n";
    
    // Test with a sample user ID
    $stmt = $pdo->query("SELECT id FROM members LIMIT 1");
    $testUserId = $stmt->fetchColumn();
    
    if ($testUserId) {
        echo "Testing with user ID: $testUserId\n";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$testUserId, $testUserId]);
        $communityRequests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Found " . count($communityRequests) . " community requests:\n";
        foreach ($communityRequests as $request) {
            echo "- ID: {$request['id']} | {$request['full_name']} | {$request['privacy_level']} | {$request['title']}\n";
        }
    }
    
    // 4. Check for requests with 'members' privacy level
    echo "\n4. REQUESTS WITH 'MEMBERS' PRIVACY LEVEL:\n";
    $stmt = $pdo->query("
        SELECT pr.id, pr.title, pr.privacy_level, pr.status, m.full_name
        FROM prayer_requests pr
        JOIN members m ON pr.member_id = m.id
        WHERE pr.privacy_level = 'members'
        ORDER BY pr.created_at DESC
    ");
    $membersRequests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($membersRequests) . " requests with 'members' privacy:\n";
    foreach ($membersRequests as $request) {
        echo "- ID: {$request['id']} | {$request['full_name']} | Status: {$request['status']} | {$request['title']}\n";
    }
    
    // 5. Check active members count
    echo "\n5. ACTIVE MEMBERS COUNT:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM members WHERE status = 'active' OR is_active = 1");
    $activeCount = $stmt->fetchColumn();
    echo "Active members: $activeCount\n";
    
    // 6. Check if there are any responses
    echo "\n6. PRAYER RESPONSES COUNT:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM prayer_responses");
    $responseCount = $stmt->fetchColumn();
    echo "Total responses: $responseCount\n";
    
    // 7. Test creating a sample request
    echo "\n7. CREATING TEST REQUEST:\n";
    if ($testUserId) {
        $stmt = $pdo->prepare("
            INSERT INTO prayer_requests (member_id, title, description, category, privacy_level, status, is_urgent, is_anonymous)
            VALUES (?, ?, ?, ?, ?, 'active', 0, 0)
        ");
        
        $stmt->execute([
            $testUserId,
            'Test Request for Visibility Debug',
            'This is a test request to debug visibility issues. It should be visible to all members.',
            'general',
            'members'
        ]);
        
        $newRequestId = $pdo->lastInsertId();
        echo "Created test request with ID: $newRequestId\n";
        
        // Test if it shows up in community requests
        $stmt = $pdo->prepare($query);
        $stmt->execute([$testUserId + 1, $testUserId + 1]); // Use different user ID
        $testResults = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $found = false;
        foreach ($testResults as $result) {
            if ($result['id'] == $newRequestId) {
                $found = true;
                break;
            }
        }
        
        echo "Test request visible to other users: " . ($found ? "YES" : "NO") . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
