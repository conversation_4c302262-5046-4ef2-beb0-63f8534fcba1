<?php
/**
 * Test the actual sendEmail function from config.php
 */

// Include the configuration file
require_once 'config.php';

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Testing sendEmail function...\n";
echo "Sending test <NAME_EMAIL>\n\n";

// Test the sendEmail function
$to = '<EMAIL>';
$toName = 'Test Recipient';
$subject = 'Test Email from sendEmail Function - ' . date('Y-m-d H:i:s');
$body = '
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
    <h2 style="color: #333;">sendEmail Function Test</h2>
    <p>This email was sent using the main sendEmail function from config.php to verify the fix is working.</p>
    <p><strong>Test Details:</strong></p>
    <ul>
        <li>Sent at: ' . date('Y-m-d H:i:s') . '</li>
        <li>Function: sendEmail() from config.php</li>
        <li>Fix applied: Port/encryption mismatch corrected</li>
    </ul>
    <p>If you receive this email, the email system is now working correctly after the fix.</p>
</div>';

// Call the sendEmail function
$result = sendEmail($to, $toName, $subject, $body);

if ($result) {
    echo "SUCCESS: Email sent successfully using sendEmail function!\n";
} else {
    echo "ERROR: Email sending failed.\n";
    global $last_email_error;
    if ($last_email_error) {
        echo "Error details: " . $last_email_error . "\n";
    }
}

echo "\nTest completed at: " . date('Y-m-d H:i:s') . "\n";
?>
