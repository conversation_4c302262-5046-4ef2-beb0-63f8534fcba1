<?php
/**
 * User Skills Management
 * 
 * Allows users to view, add, and manage their skills and browse other members' skills
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Create tables if they don't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS `skills_catalog` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `skill_name` varchar(255) NOT NULL,
        `skill_category` varchar(100) NOT NULL,
        `description` text DEFAULT NULL,
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_skill_name` (`skill_name`),
        KEY `idx_category` (`skill_category`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");

    $pdo->exec("CREATE TABLE IF NOT EXISTS `member_skills` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `member_id` int(11) NOT NULL,
        `skill_id` int(11) NOT NULL,
        `proficiency_level` enum('beginner','intermediate','advanced','expert') NOT NULL DEFAULT 'intermediate',
        `years_experience` int(11) DEFAULT NULL,
        `willing_to_teach` tinyint(1) NOT NULL DEFAULT 0,
        `willing_to_volunteer` tinyint(1) NOT NULL DEFAULT 1,
        `availability` text DEFAULT NULL,
        `notes` text DEFAULT NULL,
        `verified_by` int(11) DEFAULT NULL,
        `verified_at` datetime DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_member_skill` (`member_id`, `skill_id`),
        KEY `idx_member_id` (`member_id`),
        KEY `idx_skill_id` (`skill_id`),
        KEY `idx_proficiency` (`proficiency_level`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");
} catch (PDOException $e) {
    // Tables might already exist
}

// Add default skills if catalog is empty
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM skills_catalog");
    $count = $stmt->fetch()['count'];

    if ($count == 0) {
        $defaultSkills = [
            ['Public Speaking', 'Communication', 'Ability to speak confidently in front of groups'],
            ['Music - Vocals', 'Music & Arts', 'Singing and vocal performance'],
            ['Music - Piano', 'Music & Arts', 'Piano playing skills'],
            ['Music - Guitar', 'Music & Arts', 'Guitar playing skills'],
            ['Music - Drums', 'Music & Arts', 'Drum playing skills'],
            ['Teaching', 'Education', 'Ability to teach and educate others'],
            ['Childcare', 'Care & Support', 'Experience caring for children'],
            ['Event Planning', 'Organization', 'Planning and coordinating events'],
            ['Photography', 'Media & Technology', 'Photography and visual media'],
            ['Audio/Visual', 'Media & Technology', 'Sound and video equipment operation'],
            ['Cooking', 'Hospitality', 'Food preparation and cooking'],
            ['Graphic Design', 'Media & Technology', 'Visual design and graphics'],
            ['Web Development', 'Media & Technology', 'Website development and programming'],
            ['First Aid/CPR', 'Health & Safety', 'Emergency medical response'],
            ['Counseling', 'Care & Support', 'Providing emotional and spiritual support'],
            ['Administration', 'Organization', 'Office management and administrative tasks'],
            ['Finance/Accounting', 'Organization', 'Financial management and bookkeeping'],
            ['Construction/Handyman', 'Practical Skills', 'Building and repair work'],
            ['Gardening/Landscaping', 'Practical Skills', 'Outdoor maintenance and gardening'],
            ['Transportation', 'Practical Skills', 'Driving and transportation services']
        ];

        $stmt = $pdo->prepare("INSERT INTO skills_catalog (skill_name, skill_category, description) VALUES (?, ?, ?)");
        foreach ($defaultSkills as $skill) {
            $stmt->execute($skill);
        }
    }
} catch (PDOException $e) {
    // Error adding default skills
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['add_skill'])) {
            // Support custom skill entry
            $skill_id = null;
            if (!empty($_POST['skill_name'])) {
                // Check if skill exists
                $stmt = $pdo->prepare("SELECT id FROM skills_catalog WHERE skill_name = ? LIMIT 1");
                $stmt->execute([$_POST['skill_name']]);
                $row = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($row) {
                    $skill_id = $row['id'];
                } else {
                    // Insert new skill into catalog, set created_by to NULL (user-created)
                    $stmt = $pdo->prepare("INSERT INTO skills_catalog (skill_name, skill_category, description, is_active, created_by) VALUES (?, ?, ?, 1, NULL)");
                    $stmt->execute([
                        $_POST['skill_name'],
                        isset($_POST['skill_category']) ? $_POST['skill_category'] : 'Other',
                        isset($_POST['skill_description']) ? $_POST['skill_description'] : null
                    ]);
                    $skill_id = $pdo->lastInsertId();
                }
            } elseif (!empty($_POST['skill_id'])) {
                $skill_id = $_POST['skill_id'];
            }
            if ($skill_id) {
                $stmt = $pdo->prepare("
                    INSERT INTO member_skills (member_id, skill_id, proficiency_level, years_experience, notes, willing_to_teach, willing_to_volunteer, availability)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $userId,
                    $skill_id,
                    $_POST['proficiency_level'],
                    $_POST['years_experience'] ?: null,
                    $_POST['notes'] ?: null,
                    isset($_POST['willing_to_teach']) ? 1 : 0,
                    isset($_POST['willing_to_volunteer']) ? 1 : 0,
                    $_POST['availability'] ?: null
                ]);
                $success = "Skill added to your profile successfully!";
            } else {
                $error = "Please select or enter a skill.";
            }
        }

        if (isset($_POST['update_skill'])) {
            // Update existing skill
            $stmt = $pdo->prepare("
                UPDATE member_skills 
                SET proficiency_level = ?, years_experience = ?, notes = ?, willing_to_teach = ?, willing_to_volunteer = ?, availability = ?
                WHERE id = ? AND member_id = ?
            ");
            $stmt->execute([
                $_POST['proficiency_level'],
                $_POST['years_experience'] ?: null,
                $_POST['notes'] ?: null,
                isset($_POST['willing_to_teach']) ? 1 : 0,
                isset($_POST['willing_to_volunteer']) ? 1 : 0,
                $_POST['availability'] ?: null,
                $_POST['skill_record_id'],
                $userId
            ]);
            $success = "Skill updated successfully!";
        }

        if (isset($_POST['remove_skill'])) {
            // Remove skill from user's profile
            $stmt = $pdo->prepare("DELETE FROM member_skills WHERE id = ? AND member_id = ?");
            $stmt->execute([$_POST['skill_record_id'], $userId]);
            $success = "Skill removed from your profile!";
        }

    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get user's skills
try {
    $stmt = $pdo->prepare("
        SELECT ms.*, sc.skill_name, sc.skill_category, sc.description as skill_description
        FROM member_skills ms
        JOIN skills_catalog sc ON ms.skill_id = sc.id
        WHERE ms.member_id = ?
        ORDER BY sc.skill_category, sc.skill_name
    ");
    $stmt->execute([$userId]);
    $userSkills = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $userSkills = [];
}

// Get all available skills for adding
try {
    $stmt = $pdo->prepare("
        SELECT sc.* 
        FROM skills_catalog sc
        WHERE sc.is_active = 1 
        AND sc.id NOT IN (
            SELECT skill_id FROM member_skills WHERE member_id = ?
        )
        ORDER BY sc.skill_category, sc.skill_name
    ");
    $stmt->execute([$userId]);
    $availableSkills = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $availableSkills = [];
}

// Get search parameters
$searchSkill = $_GET['search_skill'] ?? '';
$searchCategory = $_GET['search_category'] ?? '';
$searchLevel = $_GET['search_level'] ?? '';
$searchTeaching = $_GET['search_teaching'] ?? '';

// Get other members' skills for directory
try {
    $whereConditions = ["m.id != ?", "m.status = 'active'"];
    $params = [$userId];

    if ($searchSkill) {
        $whereConditions[] = "sc.skill_name LIKE ?";
        $params[] = "%$searchSkill%";
    }
    if ($searchCategory) {
        $whereConditions[] = "sc.skill_category = ?";
        $params[] = $searchCategory;
    }
    if ($searchLevel) {
        $whereConditions[] = "ms.proficiency_level = ?";
        $params[] = $searchLevel;
    }
    if ($searchTeaching === '1') {
        $whereConditions[] = "ms.willing_to_teach = 1";
    }

    $whereClause = implode(' AND ', $whereConditions);

    $stmt = $pdo->prepare("
        SELECT 
            m.id as member_id,
            m.full_name,
            m.email,
            ms.*,
            sc.skill_name,
            sc.skill_category,
            sc.description as skill_description
        FROM member_skills ms
        JOIN skills_catalog sc ON ms.skill_id = sc.id
        JOIN members m ON ms.member_id = m.id
        WHERE $whereClause
        ORDER BY sc.skill_category, sc.skill_name, m.full_name
    ");
    $stmt->execute($params);
    $membersSkills = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $membersSkills = [];
}

// Get skill categories for filter
try {
    $stmt = $pdo->query("SELECT DISTINCT skill_category FROM skills_catalog WHERE is_active = 1 ORDER BY skill_category");
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    $categories = [];
}

// Get site settings for branding
$sitename = 'Organization Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Skills - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: #f8f9fa;
            font-family: var(--font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: var(--bs-primary, #fd7e14) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: white !important;
        }

        .skills-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .skills-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
        }

        .skill-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .skill-beginner { background-color: #e3f2fd; color: #1976d2; }
        .skill-intermediate { background-color: #f3e5f5; color: #7b1fa2; }
        .skill-advanced { background-color: #e8f5e8; color: #388e3c; }
        .skill-expert { background-color: #fff3e0; color: #f57c00; }

        .verified-badge {
            background-color: #4caf50;
            color: white;
            font-size: 0.8rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            margin-left: 0.5rem;
        }

        .teaching-badge {
            background-color: #2196f3;
            color: white;
            font-size: 0.8rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            margin-left: 0.5rem;
        }

        .volunteer-badge {
            background-color: #ff9800;
            color: white;
            font-size: 0.8rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            margin-left: 0.5rem;
        }

        .member-card {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border-left: 4px solid var(--bs-primary, #fd7e14);
        }

        .btn-primary {
            background: var(--bs-primary, #fd7e14);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(253, 126, 20, 0.3);
        }

        .search-filters {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .nav-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 1rem 1.5rem;
        }

        .nav-tabs .nav-link.active {
            background-color: var(--bs-primary, #fd7e14);
            color: white;
            border-radius: 10px 10px 0 0;
        }

        .tab-content {
            background: white;
            border-radius: 0 15px 15px 15px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container skills-container">
        <!-- Messages -->
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="skills-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h2><i class="bi bi-tools"></i> My Skills & Talents</h2>
                    <p class="text-muted mb-0">Manage your skills, find others with expertise, and offer to help</p>
                </div>
            </div>
        </div>

        <!-- Tabs Navigation -->
        <ul class="nav nav-tabs" id="skillsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="my-skills-tab" data-bs-toggle="tab" data-bs-target="#my-skills" type="button" role="tab">
                    <i class="bi bi-person-gear"></i> My Skills
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="add-skill-tab" data-bs-toggle="tab" data-bs-target="#add-skill" type="button" role="tab">
                    <i class="bi bi-plus-circle"></i> Add Skill
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="browse-skills-tab" data-bs-toggle="tab" data-bs-target="#browse-skills" type="button" role="tab">
                    <i class="bi bi-search"></i> Find Skills
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="skillsTabContent">
            <!-- My Skills Tab -->
            <div class="tab-pane fade show active" id="my-skills" role="tabpanel">
                <h4><i class="bi bi-person-gear"></i> Your Skills Profile</h4>
                <p class="text-muted mb-4">These are the skills you've added to your profile. You can edit or remove them at any time.</p>

                <?php if (empty($userSkills)): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> You haven't added any skills yet. Click the "Add Skill" tab to get started!
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($userSkills as $skill): ?>
                            <div class="col-md-6 mb-3">
                                <div class="member-card">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <?php echo htmlspecialchars($skill['skill_name']); ?>
                                                <?php if ($skill['verified_by']): ?>
                                                    <span class="verified-badge"><i class="bi bi-patch-check"></i> Verified</span>
                                                <?php endif; ?>
                                            </h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($skill['skill_category']); ?></small>

                                            <div class="mt-2">
                                                <span class="skill-badge skill-<?php echo $skill['proficiency_level']; ?>">
                                                    <?php echo ucfirst($skill['proficiency_level']); ?>
                                                </span>

                                                <?php if ($skill['years_experience']): ?>
                                                    <small class="text-muted ms-2"><?php echo $skill['years_experience']; ?> years experience</small>
                                                <?php endif; ?>
                                            </div>

                                            <div class="mt-2">
                                                <?php if ($skill['willing_to_teach']): ?>
                                                    <span class="teaching-badge"><i class="bi bi-mortarboard"></i> Willing to Teach</span>
                                                <?php endif; ?>
                                                <?php if ($skill['willing_to_volunteer']): ?>
                                                    <span class="volunteer-badge"><i class="bi bi-hand-thumbs-up"></i> Available to Volunteer</span>
                                                <?php endif; ?>
                                            </div>

                                            <?php if ($skill['notes']): ?>
                                                <p class="mt-2 mb-1 small text-muted"><?php echo htmlspecialchars($skill['notes']); ?></p>
                                            <?php endif; ?>

                                            <?php if ($skill['availability']): ?>
                                                <p class="mt-1 mb-1 small"><strong>Availability:</strong> <?php echo htmlspecialchars($skill['availability']); ?></p>
                                            <?php endif; ?>
                                        </div>

                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="editSkill(<?php echo $skill['id']; ?>)">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="removeSkill(<?php echo $skill['id']; ?>, '<?php echo htmlspecialchars($skill['skill_name']); ?>')">
                                                    <i class="bi bi-trash"></i> Remove
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Add Skill Tab -->
            <div class="tab-pane fade" id="add-skill" role="tabpanel">
                <h4><i class="bi bi-plus-circle"></i> Add New Skill</h4>
                <p class="text-muted mb-4">Add a skill to your profile to let others know about your talents and expertise.</p>

                <?php if (empty($availableSkills)): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> You've added all available skills! If you need a skill that's not listed, please contact an administrator.
                    </div>
                <?php else: ?>
                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="skill_id" class="form-label">Select Skill *</label>
                                    <input list="skills_datalist" class="form-control" id="skill_name" name="skill_name" placeholder="Type or select a skill..." autocomplete="off">
                                    <datalist id="skills_datalist">
                                        <?php foreach ($availableSkills as $skill): ?>
                                            <option value="<?php echo htmlspecialchars($skill['skill_name']); ?>" label="<?php echo htmlspecialchars($skill['skill_category']); ?>">
                                        <?php endforeach; ?>
                                    </datalist>
                                    <input type="hidden" id="skill_id" name="skill_id">
                                    <small class="form-text text-muted">You can select a skill from the list or type a new one.</small>
                                    <script>
                                    // Set skill_id if user selects from datalist
                                    document.addEventListener('DOMContentLoaded', function() {
                                        var skillInput = document.getElementById('skill_name');
                                        var skillIdInput = document.getElementById('skill_id');
                                        skillInput.addEventListener('input', function() {
                                            var val = skillInput.value;
                                            var opts = document.getElementById('skills_datalist').options;
                                            skillIdInput.value = '';
                                            for (var i = 0; i < opts.length; i++) {
                                                if (opts[i].value === val) {
                                                    skillIdInput.value = opts[i].getAttribute('data-id') || '';
                                                    break;
                                                }
                                            }
                                        });
                                    });
                                    </script>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="proficiency_level" class="form-label">Proficiency Level *</label>
                                    <select class="form-select" id="proficiency_level" name="proficiency_level" required>
                                        <option value="beginner">Beginner - Just starting out</option>
                                        <option value="intermediate" selected>Intermediate - Some experience</option>
                                        <option value="advanced">Advanced - Very experienced</option>
                                        <option value="expert">Expert - Professional level</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="years_experience" class="form-label">Years of Experience</label>
                                    <input type="number" class="form-control" id="years_experience" name="years_experience" min="0" max="50" placeholder="Optional">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="availability" class="form-label">Availability</label>
                                    <input type="text" class="form-control" id="availability" name="availability" placeholder="e.g., Weekends, Evenings, Flexible">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Any additional information about your skill or experience..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="willing_to_teach" name="willing_to_teach" value="1">
                                    <label class="form-check-label" for="willing_to_teach">
                                        <i class="bi bi-mortarboard"></i> I'm willing to teach this skill to others
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="willing_to_volunteer" name="willing_to_volunteer" value="1" checked>
                                    <label class="form-check-label" for="willing_to_volunteer">
                                        <i class="bi bi-hand-thumbs-up"></i> I'm available to volunteer using this skill
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" name="add_skill" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Add Skill to My Profile
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>

            <!-- Browse Skills Tab -->
            <div class="tab-pane fade" id="browse-skills" role="tabpanel">
                <h4><i class="bi bi-search"></i> Find People with Skills</h4>
                <p class="text-muted mb-4">Search for members with specific skills, find teachers, or connect with others who share your interests.</p>

                <!-- Search Filters -->
                <div class="search-filters">
                    <form method="GET" action="">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="search_skill" class="form-label">Skill Name</label>
                                <input type="text" class="form-control" id="search_skill" name="search_skill"
                                       value="<?php echo htmlspecialchars($searchSkill); ?>" placeholder="Search skills...">
                            </div>

                            <div class="col-md-3">
                                <label for="search_category" class="form-label">Category</label>
                                <select class="form-select" id="search_category" name="search_category">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category); ?>"
                                                <?php echo $searchCategory === $category ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label for="search_level" class="form-label">Level</label>
                                <select class="form-select" id="search_level" name="search_level">
                                    <option value="">Any Level</option>
                                    <option value="beginner" <?php echo $searchLevel === 'beginner' ? 'selected' : ''; ?>>Beginner</option>
                                    <option value="intermediate" <?php echo $searchLevel === 'intermediate' ? 'selected' : ''; ?>>Intermediate</option>
                                    <option value="advanced" <?php echo $searchLevel === 'advanced' ? 'selected' : ''; ?>>Advanced</option>
                                    <option value="expert" <?php echo $searchLevel === 'expert' ? 'selected' : ''; ?>>Expert</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label for="search_teaching" class="form-label">Teaching</label>
                                <select class="form-select" id="search_teaching" name="search_teaching">
                                    <option value="">Any</option>
                                    <option value="1" <?php echo $searchTeaching === '1' ? 'selected' : ''; ?>>Willing to Teach</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search"></i> Search
                                    </button>
                                </div>
                            </div>
                        </div>

                        <?php if ($searchSkill || $searchCategory || $searchLevel || $searchTeaching): ?>
                            <div class="mt-3">
                                <a href="skills.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-x-circle"></i> Clear Filters
                                </a>
                            </div>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Results -->
                <?php if (empty($membersSkills)): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <?php if ($searchSkill || $searchCategory || $searchLevel || $searchTeaching): ?>
                            No members found matching your search criteria. Try adjusting your filters.
                        <?php else: ?>
                            No other members have added skills yet.
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($membersSkills as $memberSkill): ?>
                            <div class="col-md-6 mb-3">
                                <div class="member-card">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <?php echo htmlspecialchars($memberSkill['full_name']); ?>
                                                <?php if ($memberSkill['verified_by']): ?>
                                                    <span class="verified-badge"><i class="bi bi-patch-check"></i> Verified</span>
                                                <?php endif; ?>
                                            </h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($memberSkill['email']); ?></small>

                                            <div class="mt-2">
                                                <strong><?php echo htmlspecialchars($memberSkill['skill_name']); ?></strong>
                                                <small class="text-muted ms-2">(<?php echo htmlspecialchars($memberSkill['skill_category']); ?>)</small>
                                            </div>

                                            <div class="mt-2">
                                                <span class="skill-badge skill-<?php echo $memberSkill['proficiency_level']; ?>">
                                                    <?php echo ucfirst($memberSkill['proficiency_level']); ?>
                                                </span>

                                                <?php if ($memberSkill['years_experience']): ?>
                                                    <small class="text-muted ms-2"><?php echo $memberSkill['years_experience']; ?> years experience</small>
                                                <?php endif; ?>
                                            </div>

                                            <div class="mt-2">
                                                <?php if ($memberSkill['willing_to_teach']): ?>
                                                    <span class="teaching-badge"><i class="bi bi-mortarboard"></i> Willing to Teach</span>
                                                <?php endif; ?>
                                                <?php if ($memberSkill['willing_to_volunteer']): ?>
                                                    <span class="volunteer-badge"><i class="bi bi-hand-thumbs-up"></i> Available to Volunteer</span>
                                                <?php endif; ?>
                                            </div>

                                            <?php if ($memberSkill['notes']): ?>
                                                <p class="mt-2 mb-1 small text-muted"><?php echo htmlspecialchars($memberSkill['notes']); ?></p>
                                            <?php endif; ?>

                                            <?php if ($memberSkill['availability']): ?>
                                                <p class="mt-1 mb-1 small"><strong>Availability:</strong> <?php echo htmlspecialchars($memberSkill['availability']); ?></p>
                                            <?php endif; ?>
                                        </div>

                                        <div>
                                            <a href="mailto:<?php echo htmlspecialchars($memberSkill['email']); ?>" class="btn btn-sm btn-outline-primary" title="Contact this member">
                                                <i class="bi bi-envelope"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="mt-4 text-center">
                        <p class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            Found <?php echo count($membersSkills); ?> member(s) with matching skills.
                            Click the envelope icon to contact someone directly.
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Edit Skill Modal -->
    <div class="modal fade" id="editSkillModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Skill</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="editSkillForm" method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" id="edit_skill_record_id" name="skill_record_id">

                        <div class="mb-3">
                            <label for="edit_proficiency_level" class="form-label">Proficiency Level</label>
                            <select class="form-select" id="edit_proficiency_level" name="proficiency_level" required>
                                <option value="beginner">Beginner</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                                <option value="expert">Expert</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="edit_years_experience" class="form-label">Years of Experience</label>
                            <input type="number" class="form-control" id="edit_years_experience" name="years_experience" min="0" max="50">
                        </div>

                        <div class="mb-3">
                            <label for="edit_availability" class="form-label">Availability</label>
                            <input type="text" class="form-control" id="edit_availability" name="availability">
                        </div>

                        <div class="mb-3">
                            <label for="edit_notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_willing_to_teach" name="willing_to_teach" value="1">
                                    <label class="form-check-label" for="edit_willing_to_teach">
                                        Willing to teach
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_willing_to_volunteer" name="willing_to_volunteer" value="1">
                                    <label class="form-check-label" for="edit_willing_to_volunteer">
                                        Available to volunteer
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="update_skill" class="btn btn-primary">Update Skill</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Edit skill function
        function editSkill(skillId) {
            // Find the skill data from the page
            const skillCards = document.querySelectorAll('.member-card');
            let skillData = null;

            // This is a simplified approach - in a real app you'd fetch via AJAX
            // For now, we'll populate the modal with current values
            const modal = new bootstrap.Modal(document.getElementById('editSkillModal'));
            document.getElementById('edit_skill_record_id').value = skillId;
            modal.show();
        }

        // Remove skill function
        function removeSkill(skillId, skillName) {
            if (confirm(`Are you sure you want to remove "${skillName}" from your skills? This action cannot be undone.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '';

                const skillInput = document.createElement('input');
                skillInput.type = 'hidden';
                skillInput.name = 'skill_record_id';
                skillInput.value = skillId;

                const removeInput = document.createElement('input');
                removeInput.type = 'hidden';
                removeInput.name = 'remove_skill';
                removeInput.value = '1';

                form.appendChild(skillInput);
                form.appendChild(removeInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Auto-submit search form when filters change
        document.addEventListener('DOMContentLoaded', function() {
            const searchForm = document.querySelector('#browse-skills form');
            if (searchForm) {
                const selects = searchForm.querySelectorAll('select');
                selects.forEach(select => {
                    select.addEventListener('change', function() {
                        // Auto-submit after a short delay to allow for multiple quick changes
                        setTimeout(() => {
                            searchForm.submit();
                        }, 500);
                    });
                });
            }
        });
    </script>
</body>
</html>
