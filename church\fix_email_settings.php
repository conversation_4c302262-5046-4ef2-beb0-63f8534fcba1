<?php
/**
 * Fix Email Settings in Database
 * 
 * This script will correct the SMTP configuration mismatch in the database
 */

// Include the configuration file
require_once 'config.php';

echo "Fixing email settings in database...\n\n";

try {
    // Get current settings
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM email_settings WHERE setting_key IN ('smtp_port', 'smtp_secure')");
    $current_settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
    
    echo "Current settings:\n";
    echo "SMTP Port: " . ($current_settings['smtp_port'] ?? 'NOT SET') . "\n";
    echo "SMTP Security: " . ($current_settings['smtp_secure'] ?? 'NOT SET') . "\n\n";
    
    // Check if we have a mismatch
    $port = $current_settings['smtp_port'] ?? '';
    $security = $current_settings['smtp_secure'] ?? '';
    
    if ($port == '465' && $security == 'tls') {
        echo "MISMATCH DETECTED: Port 465 with TLS (should be SSL)\n";
        echo "Fixing: Setting smtp_secure to 'ssl' for port 465\n";
        
        $stmt = $pdo->prepare("UPDATE email_settings SET setting_value = 'ssl' WHERE setting_key = 'smtp_secure'");
        $stmt->execute();
        
        echo "✓ Fixed smtp_secure setting\n";
    } elseif ($port == '587' && $security == 'ssl') {
        echo "MISMATCH DETECTED: Port 587 with SSL (should be TLS)\n";
        echo "Fixing: Setting smtp_secure to 'tls' for port 587\n";
        
        $stmt = $pdo->prepare("UPDATE email_settings SET setting_value = 'tls' WHERE setting_key = 'smtp_secure'");
        $stmt->execute();
        
        echo "✓ Fixed smtp_secure setting\n";
    } else {
        echo "No mismatch detected or settings are correct\n";
    }
    
    // Also check and fix settings table
    echo "\nChecking settings table...\n";
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('smtp_port', 'smtp_encryption')");
    $settings_table = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings_table[$row['setting_key']] = $row['setting_value'];
    }
    
    echo "Settings table:\n";
    echo "SMTP Port: " . ($settings_table['smtp_port'] ?? 'NOT SET') . "\n";
    echo "SMTP Encryption: " . ($settings_table['smtp_encryption'] ?? 'NOT SET') . "\n";
    
    $settings_port = $settings_table['smtp_port'] ?? '';
    $settings_encryption = $settings_table['smtp_encryption'] ?? '';
    
    if ($settings_port == '465' && $settings_encryption == 'tls') {
        echo "MISMATCH DETECTED in settings table: Port 465 with TLS (should be SSL)\n";
        echo "Fixing: Setting smtp_encryption to 'ssl' for port 465\n";
        
        $stmt = $pdo->prepare("UPDATE settings SET setting_value = 'ssl' WHERE setting_key = 'smtp_encryption'");
        $stmt->execute();
        
        echo "✓ Fixed smtp_encryption setting in settings table\n";
    } elseif ($settings_port == '587' && $settings_encryption == 'ssl') {
        echo "MISMATCH DETECTED in settings table: Port 587 with SSL (should be TLS)\n";
        echo "Fixing: Setting smtp_encryption to 'tls' for port 587\n";
        
        $stmt = $pdo->prepare("UPDATE settings SET setting_value = 'tls' WHERE setting_key = 'smtp_encryption'");
        $stmt->execute();
        
        echo "✓ Fixed smtp_encryption setting in settings table\n";
    }
    
    // Display final settings
    echo "\n=== FINAL SETTINGS ===\n";
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM email_settings WHERE setting_key IN ('smtp_host', 'smtp_port', 'smtp_secure', 'smtp_username', 'sender_email')");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo $row['setting_key'] . ": " . $row['setting_value'] . "\n";
    }
    
    echo "\nEmail settings have been corrected!\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}
?>
