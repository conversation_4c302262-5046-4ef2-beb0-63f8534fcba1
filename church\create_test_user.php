<?php
/**
 * Create a test user for testing the notification system
 */

require_once 'config.php';

echo "Creating test user...\n\n";

try {
    // Check if test user already exists
    $stmt = $pdo->prepare("SELECT id FROM members WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    
    if ($stmt->fetch()) {
        echo "Test user already exists.\n";
        echo "Email: <EMAIL>\n";
        echo "Password: test123\n";
    } else {
        // Create a test user
        $stmt = $pdo->prepare("
            INSERT INTO members (first_name, last_name, full_name, email, password_hash, status, is_active, email_verified, created_at)
            VALUES (?, ?, ?, ?, ?, 'active', 1, 1, NOW())
        ");
        
        $password = password_hash('test123', PASSWORD_DEFAULT);
        $stmt->execute([
            'Test',
            'User',
            'Test User',
            '<EMAIL>',
            $password
        ]);
        
        $userId = $pdo->lastInsertId();
        
        echo "Test user created successfully!\n";
        echo "ID: $userId\n";
        echo "Email: <EMAIL>\n";
        echo "Password: test123\n";
        echo "Status: active\n";
        
        // Create some test notifications for this user
        echo "\nCreating test notifications...\n";
        
        $notifications = [
            [
                'title' => 'Welcome to the Church Management System!',
                'message' => 'Welcome to our church management system. You can now receive notifications about important announcements, events, and messages.',
                'type' => 'announcement',
                'priority' => 'normal'
            ],
            [
                'title' => 'Upcoming Church Event',
                'message' => 'Don\'t forget about our upcoming Sunday service this weekend. We look forward to seeing you there!',
                'type' => 'event',
                'priority' => 'high'
            ],
            [
                'title' => 'System Maintenance Notice',
                'message' => 'The system will undergo maintenance this Saturday from 2 AM to 4 AM. Some features may be temporarily unavailable.',
                'type' => 'system',
                'priority' => 'low'
            ]
        ];
        
        foreach ($notifications as $notification) {
            $stmt = $pdo->prepare("
                INSERT INTO notifications (recipient_id, sender_type, notification_type, title, message, priority, created_at)
                VALUES (?, 'admin', ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $userId,
                $notification['type'],
                $notification['title'],
                $notification['message'],
                $notification['priority']
            ]);
        }
        
        echo "Created " . count($notifications) . " test notifications.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
