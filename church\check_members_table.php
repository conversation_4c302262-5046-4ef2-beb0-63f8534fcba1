<?php
/**
 * Check members table structure and users
 */

require_once 'config.php';

echo "Checking members table structure...\n\n";

try {
    // Get table structure
    $stmt = $pdo->query("DESCRIBE members");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Members table columns:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})\n";
    }
    
    echo "\nChecking for existing users...\n";
    
    // Get first few users
    $stmt = $pdo->query("SELECT id, first_name, last_name, full_name, email, status FROM members LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "No users found in the system.\n";
        
        // Create a test user
        echo "Creating a test user...\n";
        $stmt = $pdo->prepare("
            INSERT INTO members (first_name, last_name, full_name, email, password, status, created_at)
            VALUES (?, ?, ?, ?, ?, 'active', NOW())
        ");
        
        $password = password_hash('test123', PASSWORD_DEFAULT);
        $stmt->execute([
            'Test',
            'User',
            'Test User',
            '<EMAIL>',
            $password
        ]);
        
        echo "Test user created:\n";
        echo "Email: <EMAIL>\n";
        echo "Password: test123\n";
        
    } else {
        echo "Found users:\n";
        foreach ($users as $user) {
            echo "ID: {$user['id']}\n";
            echo "Name: {$user['full_name']}\n";
            echo "Email: {$user['email']}\n";
            echo "Status: {$user['status']}\n";
            echo "---\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
