<?php
/**
 * Check existing notification and message tables
 */

require_once 'config.php';

echo "Checking existing notification and message tables...\n\n";

try {
    // Check for notification-related tables
    $stmt = $pdo->query("SHOW TABLES LIKE '%notification%'");
    $notification_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Notification-related tables:\n";
    if (empty($notification_tables)) {
        echo "- No notification tables found\n";
    } else {
        foreach ($notification_tables as $table) {
            echo "- $table\n";
        }
    }
    
    // Check for message-related tables
    $stmt = $pdo->query("SHOW TABLES LIKE '%message%'");
    $message_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "\nMessage-related tables:\n";
    if (empty($message_tables)) {
        echo "- No message tables found\n";
    } else {
        foreach ($message_tables as $table) {
            echo "- $table\n";
        }
    }
    
    // Check all tables to see what exists
    echo "\nAll tables in database:\n";
    $stmt = $pdo->query("SHOW TABLES");
    $all_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($all_tables as $table) {
        echo "- $table\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
