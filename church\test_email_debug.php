<?php
/**
 * Email Debug and Test Script
 * 
 * This script will:
 * 1. Check email configuration
 * 2. Test email functionality
 * 3. Send a test <NAME_EMAIL>
 * 4. Provide detailed debugging information
 */

// Include the configuration file
require_once 'config.php';

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Create debug log file
$debug_log_file = __DIR__ . '/logs/email_debug_test.log';
if (!file_exists(dirname($debug_log_file))) {
    mkdir(dirname($debug_log_file), 0755, true);
}

function debug_log($message) {
    global $debug_log_file;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($debug_log_file, "[$timestamp] $message\n", FILE_APPEND);
    echo "[$timestamp] $message\n";
}

debug_log("=== EMAIL DEBUG TEST STARTED ===");

// Check if PHPMailer is available
debug_log("Checking PHPMailer availability...");
if (!class_exists('PHPMailer\PHPMailer\PHPMailer')) {
    debug_log("PHPMailer not found, attempting to load...");
    if (file_exists(__DIR__ . '/vendor/autoload.php')) {
        require_once __DIR__ . '/vendor/autoload.php';
        debug_log("Autoloader included");
    } else {
        debug_log("ERROR: vendor/autoload.php not found!");
        exit(1);
    }
}

if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
    debug_log("PHPMailer is available");
} else {
    debug_log("ERROR: PHPMailer still not available after loading autoloader");
    exit(1);
}

// Check database connection
debug_log("Checking database connection...");
try {
    $stmt = $pdo->query("SELECT 1");
    debug_log("Database connection successful");
} catch (Exception $e) {
    debug_log("ERROR: Database connection failed: " . $e->getMessage());
    exit(1);
}

// Check email settings tables
debug_log("Checking email settings tables...");

// Check email_settings table
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'email_settings'");
    if ($stmt->rowCount() > 0) {
        debug_log("email_settings table exists");
        
        // Get settings from email_settings table
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM email_settings");
        $email_settings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $email_settings[$row['setting_key']] = $row['setting_value'];
        }
        debug_log("Email settings from email_settings table: " . json_encode($email_settings, JSON_PRETTY_PRINT));
    } else {
        debug_log("email_settings table does not exist");
    }
} catch (Exception $e) {
    debug_log("ERROR checking email_settings table: " . $e->getMessage());
}

// Check settings table for email settings
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE '%smtp%' OR setting_key LIKE '%email%' OR setting_key LIKE '%mail%'");
    $general_settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $general_settings[$row['setting_key']] = $row['setting_value'];
    }
    debug_log("Email-related settings from settings table: " . json_encode($general_settings, JSON_PRETTY_PRINT));
} catch (Exception $e) {
    debug_log("ERROR checking settings table: " . $e->getMessage());
}

// Test email configuration
debug_log("Testing email configuration...");

// Get email settings using the same logic as the sendEmail function
$emailSettings = [];

// First try email_settings table
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM email_settings");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $emailSettings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    debug_log("Could not read from email_settings table: " . $e->getMessage());
}

// If no settings found or missing required settings, try settings table
$required_keys = ['smtp_host', 'smtp_username', 'smtp_password', 'smtp_port'];
$missing_keys = array_diff($required_keys, array_keys($emailSettings));

if (!empty($missing_keys)) {
    debug_log("Missing keys from email_settings: " . implode(', ', $missing_keys));
    debug_log("Trying to get missing settings from settings table...");
    
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'from_email', 'from_name', 'reply_to_email')");
    $stmt->execute();
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $key = $row['setting_key'];
        if ($key === 'smtp_encryption') {
            $emailSettings['smtp_secure'] = $row['setting_value'];
        } elseif ($key === 'from_email') {
            $emailSettings['sender_email'] = $row['setting_value'];
        } elseif ($key === 'from_name') {
            $emailSettings['sender_name'] = $row['setting_value'];
        } elseif ($key === 'reply_to_email') {
            $emailSettings['reply_to_email'] = $row['setting_value'];
        } else {
            $emailSettings[$key] = $row['setting_value'];
        }
    }
}

debug_log("Final email settings: " . json_encode($emailSettings, JSON_PRETTY_PRINT));

// Check if we have minimum required settings
$still_missing = array_diff($required_keys, array_keys($emailSettings));
if (!empty($still_missing)) {
    debug_log("ERROR: Still missing required email settings: " . implode(', ', $still_missing));
    debug_log("Cannot proceed with email test without these settings");
    exit(1);
}

// Test email sending
debug_log("Attempting to send test <NAME_EMAIL>...");

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

try {
    $mail = new PHPMailer(true);
    
    // Enable SMTP debugging
    $mail->SMTPDebug = 2;
    $mail->Debugoutput = function($str, $level) {
        debug_log("SMTP Debug [$level]: $str");
    };
    
    // Server settings
    $mail->isSMTP();
    $mail->Host = $emailSettings['smtp_host'] ?? 'smtp.example.com';
    $mail->SMTPAuth = isset($emailSettings['smtp_auth']) && $emailSettings['smtp_auth'] == '1';
    $mail->Username = $emailSettings['smtp_username'] ?? '';
    $mail->Password = $emailSettings['smtp_password'] ?? '';

    // Fix the port/encryption mismatch
    $port = $emailSettings['smtp_port'] ?? 587;
    $security = $emailSettings['smtp_secure'] ?? 'tls';

    // Correct the configuration based on port
    if ($port == 465) {
        $mail->SMTPSecure = 'ssl';  // Port 465 requires SSL
        $mail->Port = 465;
        debug_log("Corrected: Using SSL for port 465");
    } else {
        $mail->SMTPSecure = 'tls';  // Port 587 uses TLS
        $mail->Port = 587;
        debug_log("Corrected: Using TLS for port 587");
    }
    $mail->Timeout = 30;
    $mail->SMTPKeepAlive = true;
    $mail->SMTPOptions = [
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true,
            'cafile' => false,
            'capath' => false,
            'ciphers' => 'DEFAULT:!DH'
        ]
    ];
    
    debug_log("SMTP Configuration:");
    debug_log("Host: " . $mail->Host);
    debug_log("Username: " . $mail->Username);
    debug_log("Password: " . (empty($mail->Password) ? 'NOT SET' : '[HIDDEN]'));
    debug_log("Port: " . $mail->Port);
    debug_log("Security: " . $mail->SMTPSecure);
    debug_log("Auth: " . ($mail->SMTPAuth ? 'YES' : 'NO'));
    
    // Recipients
    $senderEmail = $emailSettings['sender_email'] ?? $emailSettings['smtp_username'] ?? '<EMAIL>';
    $senderName = $emailSettings['sender_name'] ?? 'Church System';
    
    $mail->setFrom($senderEmail, $senderName);
    $mail->addAddress('<EMAIL>', 'Test Recipient');
    
    if (!empty($emailSettings['reply_to_email'])) {
        $mail->addReplyTo($emailSettings['reply_to_email'], $senderName);
    }
    
    // Content
    $mail->isHTML(true);
    $mail->Subject = 'Email System Test - ' . date('Y-m-d H:i:s');
    $mail->Body = '
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
        <h2 style="color: #333;">Email System Test</h2>
        <p>This is a test email to verify that the email system is working correctly.</p>
        <p><strong>Test Details:</strong></p>
        <ul>
            <li>Sent at: ' . date('Y-m-d H:i:s') . '</li>
            <li>SMTP Host: ' . htmlspecialchars($mail->Host) . '</li>
            <li>SMTP Port: ' . htmlspecialchars($mail->Port) . '</li>
            <li>From: ' . htmlspecialchars($senderEmail) . '</li>
        </ul>
        <p>If you receive this email, the email system is working correctly.</p>
    </div>';
    
    debug_log("Attempting to send email...");
    $mail->send();
    debug_log("SUCCESS: Email sent <NAME_EMAIL>");
    
} catch (Exception $e) {
    debug_log("ERROR: Email sending failed: " . $e->getMessage());
    debug_log("PHPMailer Error Info: " . $mail->ErrorInfo);
}

debug_log("=== EMAIL DEBUG TEST COMPLETED ===");

// Display results
echo "\n\n=== SUMMARY ===\n";
echo "Check the log file for detailed information: $debug_log_file\n";
echo "Email settings configured: " . (empty($still_missing) ? "YES" : "NO") . "\n";
if (!empty($still_missing)) {
    echo "Missing settings: " . implode(', ', $still_missing) . "\n";
}
echo "Test completed at: " . date('Y-m-d H:i:s') . "\n";
?>
