<?php
/**
 * Setup Automatic Notifications System
 * 
 * This script sets up the automatic notification system by:
 * 1. Adding community notification type to existing users
 * 2. Creating sample notifications to test the system
 */

require_once 'config.php';
require_once 'includes/automatic_notifications.php';

echo "Setting up automatic notifications system...\n\n";

try {
    // 1. Add community notification type to notification preferences
    echo "1. Adding community notification preferences...\n";
    addCommunityNotificationPreferences($pdo);
    echo "✓ Community notification preferences added\n";
    
    // 2. Update notification types enum to include community
    echo "\n2. Updating notification types...\n";
    try {
        $pdo->exec("
            ALTER TABLE notifications 
            MODIFY COLUMN notification_type ENUM('announcement', 'message', 'birthday', 'event', 'donation', 'system', 'community') 
            NOT NULL DEFAULT 'message'
        ");
        echo "✓ Notification types updated\n";
    } catch (Exception $e) {
        echo "Note: " . $e->getMessage() . "\n";
    }
    
    // 3. Update notification preferences enum
    try {
        $pdo->exec("
            ALTER TABLE notification_preferences 
            MODIFY COLUMN notification_type ENUM('announcement', 'message', 'birthday', 'event', 'donation', 'system', 'community') 
            NOT NULL
        ");
        echo "✓ Notification preferences types updated\n";
    } catch (Exception $e) {
        echo "Note: " . $e->getMessage() . "\n";
    }
    
    // 4. Test the automatic notification system
    echo "\n3. Testing automatic notification system...\n";
    
    // Get test users
    $stmt = $pdo->query("SELECT id, full_name FROM members WHERE (status = 'active' OR is_active = 1) LIMIT 3");
    $testUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($testUsers) >= 2) {
        $user1 = $testUsers[0];
        $user2 = $testUsers[1];
        
        echo "Testing with users: {$user1['full_name']} and {$user2['full_name']}\n";
        
        // Test skill notification
        echo "- Testing skill notification...\n";
        $result = notifyNewSkillPosted($pdo, $user1['id'], 'Test Skill', 'Technology', 'Expert');
        echo $result ? "✓ Skill notification sent\n" : "✗ Skill notification failed\n";
        
        // Test request notification
        echo "- Testing request notification...\n";
        $result = notifyNewRequestCreated($pdo, $user1['id'], 999, 'Test Request', 'general', 'members', false);
        echo $result ? "✓ Request notification sent\n" : "✗ Request notification failed\n";
        
        // Test comment notification
        echo "- Testing comment notification...\n";
        $result = notifyNewRequestComment($pdo, $user2['id'], 999, $user1['id'], 'Test Request', 'This is a test comment');
        echo $result ? "✓ Comment notification sent\n" : "✗ Comment notification failed\n";
        
        // Test profile update notification
        echo "- Testing profile update notification...\n";
        $changes = ['full_name' => 'Updated Name', 'bio' => 'Updated bio'];
        $result = notifyProfileUpdate($pdo, $user1['id'], $changes);
        echo $result ? "✓ Profile update notification sent\n" : "✗ Profile update notification failed\n";
        
    } else {
        echo "Not enough test users available for testing\n";
    }
    
    // 5. Show notification statistics
    echo "\n4. Notification Statistics:\n";
    
    $stmt = $pdo->query("SELECT notification_type, COUNT(*) as count FROM notifications GROUP BY notification_type");
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($stats as $stat) {
        echo "- {$stat['notification_type']}: {$stat['count']} notifications\n";
    }
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM notifications WHERE is_read = 0");
    $unreadTotal = $stmt->fetchColumn();
    echo "- Total unread: $unreadTotal notifications\n";
    
    echo "\n✅ Automatic notifications system setup complete!\n";
    echo "\nFeatures enabled:\n";
    echo "- New skill posted notifications\n";
    echo "- New request created notifications\n";
    echo "- Request comment notifications\n";
    echo "- Profile update notifications\n";
    echo "- Notification preferences filtering\n";
    echo "- Real-time notification updates\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
